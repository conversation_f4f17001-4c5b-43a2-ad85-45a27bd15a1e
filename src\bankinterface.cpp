#include "bankinterface.h"
#include "savingsaccount.h"
#include "creditaccount.h"
#include <iostream>
#include <iomanip>
#include <limits>
#include <memory>

#ifdef _WIN32
#include <cstdlib>
#define CLEAR_SCREEN "cls"
#else
#define <PERSON><PERSON>AR_SCREEN "clear"
#endif

using namespace std;

BankInterface::BankInterface() : currentDate(2024, 1, 1) {
}

void BankInterface::run() {
    cout << "=== 欢迎使用个人银行账户管理系统 ===" << endl;

    while (true) {
        if (!userManager.isLoggedIn()) {
            showLoginMenu();
        } else {
            showMainMenu();
        }
    }
}

void BankInterface::showLoginMenu() {
    clearScreen();
    cout << "\n=== 登录/注册 ===" << endl;
    cout << "1. 登录" << endl;
    cout << "2. 注册" << endl;
    cout << "3. 退出系统" << endl;
    cout << "请选择: ";

    int choice;
    if (!(cin >> choice)) {
        cin.clear();
        cin.ignore(numeric_limits<streamsize>::max(), '\n');
        choice = -1; // 设置为无效选择
    }

    switch (choice) {
        case 1:
            if (!handleLogin()) {
                cout << "登录失败！用户名或密码错误。" << endl;
                pauseScreen();
            } else {
                cout << "登录成功！正在进入系统..." << endl;
                // 移除这里的pauseScreen()调用以自动跳转
            }
            break;
        case 2:
            handleRegister();
            break;
        case 3:
            cout << "感谢使用！再见！" << endl;
            exit(0);
        default:
            cout << "无效选择！" << endl;
            pauseScreen();
    }
}

bool BankInterface::handleLogin() {
    string username, password;
    cout << "\n=== 用户登录 ===" << endl;

    // 清除输入缓冲区
    cin.clear();
    cin.ignore(numeric_limits<streamsize>::max(), '\n');

    cout << "用户名: ";
    getline(cin, username);

    cout << "密码: ";
    getline(cin, password);

    return userManager.loginUser(username, password);
}

bool BankInterface::handleRegister() {
    string username, password, confirmPassword;

    cout << "\n=== 用户注册 ===" << endl;

    // 清除输入缓冲区
    cin.clear();
    cin.ignore(numeric_limits<streamsize>::max(), '\n');

    cout << "用户名 (3-20字符，仅字母数字下划线): ";
    getline(cin, username);

    cout << "密码 (6-50字符): ";
    getline(cin, password);

    cout << "确认密码: ";
    getline(cin, confirmPassword);

    if (password != confirmPassword) {
        cout << "\n❌ 两次输入的密码不一致！" << endl;
        pauseScreen();
        return false;
    }

    bool result = userManager.registerUser(username, password);
    if (result) {
        cout << "\n✅ 注册成功！请返回登录。" << endl;
    } else {
        cout << "\n❌ 注册失败！" << endl;
        cout << "可能原因：" << endl;
        cout << "• 用户名已存在" << endl;
        cout << "• 用户名格式不正确（要求：3-20字符，仅字母数字下划线）" << endl;
        cout << "• 密码格式不正确（要求：6-50字符）" << endl;
    }
    pauseScreen();
    return result;
}

void BankInterface::showMainMenu() {
    clearScreen();
    auto user = userManager.getCurrentUser();

    // 显示欢迎信息
    cout << "\n╔══════════════════════════════════════════════════════════════╗" << endl;
    cout << "║                    个人银行账户管理系统                        ║" << endl;
    cout << "╚══════════════════════════════════════════════════════════════╝" << endl;
    cout << "\n👤 用户: " << user->getUsername() << endl;
    cout << "📅 当前日期: ";
    currentDate.show();
    cout << endl;
    cout << "💰 总资产: " << user->getTotalBalance() << " 元" << endl;

    // 显示用户提醒
    showUserReminders();

    cout << "\n╔══════════════════════════════════════════════════════════════╗" << endl;
    cout << "║                          主菜单                              ║" << endl;
    cout << "╠══════════════════════════════════════════════════════════════╣" << endl;
    cout << "║  【账户管理】                                                 ║" << endl;
    cout << "║    1. 创建账户                                               ║" << endl;
    cout << "║    2. 存款                                                   ║" << endl;
    cout << "║    3. 取款                                                   ║" << endl;
    cout << "║    4. 显示账户                                               ║" << endl;
    cout << "║    5. 账户统计                                               ║" << endl;
    cout << "║                                                              ║" << endl;
    cout << "║  【查询功能】                                                 ║" << endl;
    cout << "║    6. 查询记录                                               ║" << endl;
    cout << "║    7. 按时间排序查询                                          ║" << endl;
    cout << "║    8. 按金额排序查询                                          ║" << endl;
    cout << "║    12. 按月查询记录                                          ║" << endl;
    cout << "║                                                              ║" << endl;
    cout << "║  【系统功能】                                                 ║" << endl;
    cout << "║    9. 更改日期                                               ║" << endl;
    cout << "║    10. 下个月                                                ║" << endl;
    cout << "║    11. 月度统计                                              ║" << endl;
    cout << "║                                                              ║" << endl;
    cout << "║    0. 退出登录                                               ║" << endl;
    cout << "╚══════════════════════════════════════════════════════════════╝" << endl;
    cout << "\n请选择功能 (输入数字 0-12): ";

    handleMainMenu();
}

void BankInterface::handleMainMenu() {
    int choice;

    // 检查输入是否有效
    if (!(cin >> choice)) {
        cin.clear();
        cin.ignore(numeric_limits<streamsize>::max(), '\n');
        cout << "\n❌ 输入无效！请输入数字 0-12。" << endl;
        pauseScreen();
        return;
    }

    try {
        switch (choice) {
            case 1:
                cout << "\n🏦 正在进入创建账户..." << endl;
                createAccount();
                break;
            case 2:
                cout << "\n💰 正在进入存款..." << endl;
                depositMoney();
                break;
            case 3:
                cout << "\n💸 正在进入取款..." << endl;
                withdrawMoney();
                break;
            case 4:
                cout << "\n📋 正在显示账户..." << endl;
                showAccounts();
                pauseScreen();
                break;
            case 5:
                cout << "\n📊 正在显示账户统计..." << endl;
                showAccountStatistics();
                break;
            case 6:
                cout << "\n🔍 正在进入查询记录..." << endl;
                queryRecords();
                break;
            case 7:
                cout << "\n⏰ 正在进入按时间排序查询..." << endl;
                queryRecordsByTime();
                break;
            case 8:
                cout << "\n💵 正在进入按金额排序查询..." << endl;
                queryRecordsByAmount();
                break;
            case 9:
                cout << "\n📅 正在进入更改日期..." << endl;
                changeDate();
                break;
            case 10:
                cout << "\n📆 正在进入下个月..." << endl;
                nextMonth();
                break;
            case 11:
                cout << "\n📈 正在进入月度统计..." << endl;
                showMonthlyStatistics();
                break;
            case 12:
                cout << "\n📝 正在进入按月查询..." << endl;
                queryMonthlyRecords();
                break;
            case 0:
                cout << "\n👋 正在退出登录..." << endl;
                userManager.logoutUser();
                cout << "✅ 已成功退出登录！" << endl;
                pauseScreen();
                return;
            default:
                cout << "\n❌ 无效选择！请输入 0-12 之间的数字。" << endl;
                cout << "💡 提示：输入对应功能前的数字即可选择该功能。" << endl;
                pauseScreen();
        }
    } catch (const exception& e) {
        handleException(e);
    }
}

void BankInterface::clearScreen() {
    system(CLEAR_SCREEN);
}

void BankInterface::pauseScreen() {
    cout << "按回车键继续...";

    // 清除错误状态
    cin.clear();

    // 清除输入缓冲区
    cin.ignore(numeric_limits<streamsize>::max(), '\n');

    // 等待用户按回车
    cin.get();
}

void BankInterface::handleException(const exception& e) {
    cout << "错误: " << e.what() << endl;

    // 如果是账户异常，显示账户信息
    const AccountException* accEx = dynamic_cast<const AccountException*>(&e);
    if (accEx && accEx->getAccount()) {
        cout << "相关账户: ";
        accEx->getAccount()->show();
        cout << endl;
    }

    pauseScreen();
}

void BankInterface::createAccount() {
    auto user = userManager.getCurrentUser();

    cout << "\n=== 创建账户 ===" << endl;
    cout << "1. 储蓄账户" << endl;
    cout << "2. 信用账户" << endl;
    cout << "选择账户类型: ";

    int type;
    cin >> type;

    string id;
    cout << "账户ID: ";
    cin >> id;

    shared_ptr<Account> account;

    if (type == 1) {
        double rate;
        cout << "年利率 (例如: 0.05): ";
        cin >> rate;
        account = make_shared<SavingsAccount>(currentDate, id, rate);
    } else if (type == 2) {
        double credit, rate, fee;
        cout << "信用额度: ";
        cin >> credit;
        cout << "日利率 (例如: 0.0005): ";
        cin >> rate;
        cout << "年费: ";
        cin >> fee;
        account = make_shared<CreditAccount>(currentDate, id, credit, rate, fee);
    } else {
        cout << "无效的账户类型！" << endl;
        pauseScreen();
        return;
    }

    user->addAccount(account);
    cout << "账户创建成功！" << endl;
    pauseScreen();
}

void BankInterface::depositMoney() {
    auto user = userManager.getCurrentUser();

    if (user->getAccountCount() == 0) {
        cout << "您还没有账户，请先创建账户！" << endl;
        pauseScreen();
        return;
    }

    cout << "\n=== 存款 ===" << endl;
    showAccounts();

    cout << "选择账户 (0-" << user->getAccountCount() - 1 << "): ";
    size_t index;
    cin >> index;

    auto account = user->getAccount(index);
    if (!account) {
        cout << "无效的账户索引！" << endl;
        pauseScreen();
        return;
    }

    double amount;
    string desc;
    cout << "存款金额: ";
    cin >> amount;
    cout << "描述: ";
    cin.ignore();
    getline(cin, desc);

    account->deposit(currentDate, amount, desc);
    cout << "存款成功！" << endl;
    pauseScreen();
}

void BankInterface::withdrawMoney() {
    auto user = userManager.getCurrentUser();

    if (user->getAccountCount() == 0) {
        cout << "您还没有账户，请先创建账户！" << endl;
        pauseScreen();
        return;
    }

    cout << "\n=== 取款 ===" << endl;
    showAccounts();

    cout << "选择账户 (0-" << user->getAccountCount() - 1 << "): ";
    size_t index;
    cin >> index;

    auto account = user->getAccount(index);
    if (!account) {
        cout << "无效的账户索引！" << endl;
        pauseScreen();
        return;
    }

    double amount;
    string desc;
    cout << "取款金额: ";
    cin >> amount;
    cout << "描述: ";
    cin.ignore();
    getline(cin, desc);

    account->withdraw(currentDate, amount, desc);
    cout << "取款成功！" << endl;
    pauseScreen();
}

void BankInterface::showAccounts() {
    auto user = userManager.getCurrentUser();

    cout << "\n=== 账户列表 ===" << endl;
    for (size_t i = 0; i < user->getAccountCount(); i++) {
        cout << "[" << i << "] ";
        user->getAccount(i)->show();
        cout << endl;
    }
}

void BankInterface::showAccountStatistics() {
    auto user = userManager.getCurrentUser();

    cout << "\n=== 账户统计 ===" << endl;
    cout << "总账户数: " << user->getAccountCount() << endl;
    cout << "总资产: " << user->getTotalBalance() << endl;

    double totalSavings = 0, totalCredit = 0;
    for (size_t i = 0; i < user->getAccountCount(); i++) {
        auto account = user->getAccount(i);
        auto creditAccount = dynamic_pointer_cast<CreditAccount>(account);
        if (creditAccount) {
            totalCredit += account->getBalance();
        } else {
            totalSavings += account->getBalance();
        }
    }

    cout << "储蓄账户总额: " << totalSavings << endl;
    cout << "信用账户总额: " << totalCredit << endl;
    pauseScreen();
}

void BankInterface::queryRecords() {
    cout << "\n=== 查询记录 ===" << endl;
    cout << "开始日期 (YYYY/MM/DD): ";
    Date startDate = Date::read();
    cout << "结束日期 (YYYY/MM/DD): ";
    Date endDate = Date::read();

    Account::query(startDate, endDate);
    pauseScreen();
}

void BankInterface::queryRecordsByTime() {
    cout << "\n=== 按时间排序查询 ===" << endl;
    cout << "开始日期 (YYYY/MM/DD): ";
    Date startDate = Date::read();
    cout << "结束日期 (YYYY/MM/DD): ";
    Date endDate = Date::read();

    Account::queryByTime(startDate, endDate);
    pauseScreen();
}

void BankInterface::queryRecordsByAmount() {
    cout << "\n=== 按金额排序查询 ===" << endl;
    cout << "开始日期 (YYYY/MM/DD): ";
    Date startDate = Date::read();
    cout << "结束日期 (YYYY/MM/DD): ";
    Date endDate = Date::read();

    Account::queryByAmount(startDate, endDate);
    pauseScreen();
}

void BankInterface::changeDate() {
    cout << "\n=== 更改日期 ===" << endl;
    cout << "当前日期: ";
    currentDate.show();
    cout << endl;

    cout << "新日期 (YYYY/MM/DD): ";
    Date newDate = Date::read();

    if (newDate < currentDate) {
        cout << "不能设置过去的日期！" << endl;
    } else {
        currentDate = newDate;
        cout << "日期已更新！" << endl;
    }
    pauseScreen();
}

void BankInterface::nextMonth() {
    cout << "\n=== 进入下个月 ===" << endl;

    if (currentDate.getMonth() == 12) {
        currentDate = Date(currentDate.getYear() + 1, 1, 1);
    } else {
        currentDate = Date(currentDate.getYear(), currentDate.getMonth() + 1, 1);
    }

    // 对所有用户账户进行结算
    auto user = userManager.getCurrentUser();
    for (size_t i = 0; i < user->getAccountCount(); i++) {
        user->getAccount(i)->settle(currentDate);
    }

    cout << "已进入下个月，所有账户已结算！" << endl;
    cout << "当前日期: ";
    currentDate.show();
    cout << endl;
    pauseScreen();
}

void BankInterface::showUserReminders() {
    auto user = userManager.getCurrentUser();
    auto reminders = user->getReminders();

    // 只显示最重要的提醒信息，保持简洁
    if (!reminders.empty()) {
        cout << "\n⚠️  重要提醒: ";
        if (reminders.size() == 1) {
            cout << reminders[0] << endl;
        } else {
            cout << "您有 " << reminders.size() << " 条重要提醒，请查看账户统计。" << endl;
        }
    }

    // 显示简化的当月统计
    int currentYear = currentDate.getYear();
    int currentMonth = currentDate.getMonth();

    double income = user->getMonthlyIncome(currentYear, currentMonth);
    double expense = user->getMonthlyExpense(currentYear, currentMonth);

    if (income > 0 || expense > 0) {
        cout << "📊 本月: 收入 " << income << " 元, 支出 " << expense << " 元" << endl;
    }
}

void BankInterface::showMonthlyStatistics() {
    auto user = userManager.getCurrentUser();

    cout << "\n=== 月度统计 ===" << endl;

    // 让用户选择查询月份
    int year, month;
    cout << "请输入年份 (例如: 2024): ";
    cin >> year;
    cout << "请输入月份 (1-12): ";
    cin >> month;

    if (month < 1 || month > 12) {
        cout << "无效的月份！" << endl;
        pauseScreen();
        return;
    }

    cout << "\n" << year << "年" << month << "月统计报告" << endl;
    cout << "================================" << endl;

    double income = user->getMonthlyIncome(year, month);
    double expense = user->getMonthlyExpense(year, month);

    cout << "📈 本月收入: " << income << " 元" << endl;
    cout << "📉 本月支出: " << expense << " 元" << endl;
    cout << "💰 净收入: " << (income - expense) << " 元" << endl;
    cout << "🏦 当前总资产: " << user->getTotalBalance() << " 元" << endl;

    // 显示收支比例
    if (income > 0) {
        double savingsRate = ((income - expense) / income) * 100;
        cout << "💾 储蓄率: " << savingsRate << "%" << endl;
    }

    pauseScreen();
}

void BankInterface::queryMonthlyRecords() {
    auto user = userManager.getCurrentUser();

    cout << "\n=== 按月查询记录 ===" << endl;

    int year, month;
    cout << "请输入年份 (例如: 2024): ";
    cin >> year;
    cout << "请输入月份 (1-12): ";
    cin >> month;

    if (month < 1 || month > 12) {
        cout << "无效的月份！" << endl;
        pauseScreen();
        return;
    }

    cout << "\n" << year << "年" << month << "月的交易记录:" << endl;
    cout << "================================" << endl;

    // 构造该月的开始和结束日期
    Date startDate(year, month, 1);
    int endDay = 31;
    if (month == 2) {
        endDay = 28; // 简化处理，不考虑闰年
    } else if (month == 4 || month == 6 || month == 9 || month == 11) {
        endDay = 30;
    }
    Date endDate(year, month, endDay);

    // 获取所有记录并筛选用户的记录
    const auto& recordMap = Account::getRecordMap();
    bool hasRecords = false;

    for (const auto& record : recordMap) {
        const Date& date = record.first;
        const AccountRecord& accountRecord = record.second;

        // 检查是否在指定月份范围内
        if (date.getYear() == year && date.getMonth() == month) {
            // 检查是否是当前用户的账户
            bool isUserAccount = false;
            for (const auto& account : user->getAccounts()) {
                if (account->getId() == accountRecord.getAccount()) {
                    isUserAccount = true;
                    break;
                }
            }

            if (isUserAccount) {
                accountRecord.show();
                hasRecords = true;
            }
        }
    }

    if (!hasRecords) {
        cout << "该月份没有交易记录。" << endl;
    }

    pauseScreen();
}



